/**
 * Interface de la Mémoire Thermique
 * Ce module gère l'affichage et l'interaction avec la mémoire thermique
 * et fournit une visualisation interactive de son fonctionnement
 */

class ThermalMemoryInterface {
    constructor() {
        this.initialized = false;
        this.memoryLevels = 5;
        this.connector = window.thermalMemoryConnector;
        this.memoryVisualization = null;
        this.memoryStats = {
            capacity: 0,
            used: 0, 
            temperature: 37.2,
            efficiency: 0.85,
            retrievalAccuracy: 0.93,
            consolidationRate: 0.78
        };
        this.memoryCategories = {
            code: { count: 0, color: '#4287f5' },
            concepts: { count: 0, color: '#42f584' },
            patterns: { count: 0, color: '#f5a442' },
            paradigms: { count: 0, color: '#f542e9' },
            innovations: { count: 0, color: '#f54242' }
        };
        this.memoryItems = [];
    }

    /**
     * Initialise l'interface de la mémoire thermique
     */
    initialize() {
        if (this.initialized) return;
        
        console.log('Initialisation de l\'interface de mémoire thermique...');
        
        // Initialiser la connexion au connecteur
        if (this.connector) {
            this.connector.addMemoryStatusListener(this.updateStatus.bind(this));
        } else {
            console.warn('Connecteur non disponible pour l\'interface de mémoire thermique');
        }
        
        // Initialiser l'interface visuelle
        this.initializeUI();
        this.initializeVisualization();
        this.initializeEventListeners();
        
        // Charger les données initiales
        this.loadMemoryData();
        
        this.initialized = true;
    }
    
    /**
     * Initialise les éléments d'interface utilisateur
     */
    initializeUI() {
        const memorySection = document.getElementById('memory-section');
        if (!memorySection) return;
        
        // Vérifier si les éléments existent déjà
        if (document.getElementById('memory-visualization-container')) return;
        
        // Créer le conteneur de visualisation
        const visualizationContainer = document.createElement('div');
        visualizationContainer.id = 'memory-visualization-container';
        visualizationContainer.className = 'memory-visualization-container';
        
        // Créer le conteneur de statistiques
        const statsContainer = document.createElement('div');
        statsContainer.id = 'memory-stats-container';
        statsContainer.className = 'memory-stats-container';
        
        // Structure du conteneur de statistiques
        statsContainer.innerHTML = `
            <h3>Statistiques de la Mémoire Thermique</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-label">Température</div>
                    <div id="memory-temperature" class="stat-value">${this.memoryStats.temperature}°C</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">Efficacité</div>
                    <div id="memory-efficiency" class="stat-value">${Math.round(this.memoryStats.efficiency * 100)}%</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">Précision de récupération</div>
                    <div id="memory-retrieval" class="stat-value">${Math.round(this.memoryStats.retrievalAccuracy * 100)}%</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">Taux de consolidation</div>
                    <div id="memory-consolidation" class="stat-value">${Math.round(this.memoryStats.consolidationRate * 100)}%</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">Capacité utilisée</div>
                    <div id="memory-capacity" class="stat-value">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${(this.memoryStats.used / this.memoryStats.capacity) * 100}%"></div>
                        </div>
                        <div class="progress-text">${this.memoryStats.used}/${this.memoryStats.capacity}</div>
                    </div>
                </div>
            </div>
        `;
        
        // Créer le conteneur d'éléments de mémoire
        const itemsContainer = document.createElement('div');
        itemsContainer.id = 'memory-items-container';
        itemsContainer.className = 'memory-items-container';
        
        // Structure du conteneur d'éléments
        itemsContainer.innerHTML = `
            <h3>Éléments de Mémoire</h3>
            <div class="memory-filters">
                <button class="filter-button active" data-filter="all">Tous</button>
                <button class="filter-button" data-filter="code">Code</button>
                <button class="filter-button" data-filter="concepts">Concepts</button>
                <button class="filter-button" data-filter="patterns">Patterns</button>
                <button class="filter-button" data-filter="paradigms">Paradigmes</button>
                <button class="filter-button" data-filter="innovations">Innovations</button>
            </div>
            <div id="memory-items-list" class="memory-items-list">
                <div class="loading-indicator">Chargement des éléments de mémoire...</div>
            </div>
        `;
        
        // Ajouter les conteneurs à la section de mémoire
        memorySection.appendChild(visualizationContainer);
        memorySection.appendChild(statsContainer);
        memorySection.appendChild(itemsContainer);
    }
    
    /**
     * Initialise la visualisation de la mémoire thermique
     */
    initializeVisualization() {
        const container = document.getElementById('memory-visualization-container');
        if (!container) return;
        
        // Créer le canvas pour la visualisation
        const canvas = document.createElement('canvas');
        canvas.id = 'memory-visualization';
        canvas.width = container.clientWidth;
        canvas.height = 300;
        container.appendChild(canvas);
        
        const ctx = canvas.getContext('2d');
        
        // Initialiser la visualisation
        // (Utilisera Chart.js pour créer une visualisation de la mémoire thermique)
        this.memoryVisualization = new Chart(ctx, {
            type: 'polarArea',
            data: {
                labels: Object.keys(this.memoryCategories),
                datasets: [{
                    data: Object.values(this.memoryCategories).map(cat => cat.count),
                    backgroundColor: Object.values(this.memoryCategories).map(cat => cat.color),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Répartition de la Mémoire Thermique',
                        font: { size: 16 }
                    },
                    legend: {
                        position: 'right'
                    },
                    tooltip: {
                        callbacks: {
                            label: (item) => `${item.label}: ${item.raw} éléments`
                        }
                    }
                }
            }
        });
    }
    
    /**
     * Initialise les écouteurs d'événements pour l'interface
     */
    initializeEventListeners() {
        // Écouteurs pour les filtres
        const filterButtons = document.querySelectorAll('.filter-button');
        filterButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                // Retirer la classe active de tous les boutons
                filterButtons.forEach(btn => btn.classList.remove('active'));
                // Ajouter la classe active au bouton cliqué
                e.target.classList.add('active');
                
                // Filtrer les éléments
                const filter = e.target.dataset.filter;
                this.filterMemoryItems(filter);
            });
        });
        
        // Écouteur pour le bouton d'analyse de code
        const analyzeCodeBtn = document.getElementById('analyze-code-btn');
        if (analyzeCodeBtn) {
            analyzeCodeBtn.addEventListener('click', () => {
                this.showCodeAnalyzer();
            });
        }
        
        // Écouteur pour le bouton d'exploration de paradigme
        const exploreParadigmBtn = document.getElementById('explore-paradigm-btn');
        if (exploreParadigmBtn) {
            exploreParadigmBtn.addEventListener('click', () => {
                this.showParadigmExplorer();
            });
        }
    }
    
    /**
     * Charge les données de mémoire depuis le serveur
     */
    loadMemoryData() {
        console.log('Chargement des données de mémoire...');
        
        // Simuler le chargement des données (à remplacer par une vraie requête API)
        setTimeout(() => {
            // Simuler des statistiques
            this.memoryStats = {
                capacity: 10000,
                used: 3547,
                temperature: 37.8,
                efficiency: 0.87,
                retrievalAccuracy: 0.95,
                consolidationRate: 0.82
            };
            
            // Simuler des catégories
            this.memoryCategories = {
                code: { count: 1245, color: '#4287f5' },
                concepts: { count: 876, color: '#42f584' },
                patterns: { count: 693, color: '#f5a442' },
                paradigms: { count: 421, color: '#f542e9' },
                innovations: { count: 312, color: '#f54242' }
            };
            
            // Simuler des éléments de mémoire
            this.memoryItems = this.generateSampleMemoryItems();
            
            // Mettre à jour l'interface
            this.updateStats();
            this.updateVisualization();
            this.renderMemoryItems();
        }, 1000);
    }
    
    /**
     * Génère des éléments de mémoire exemple
     */
    generateSampleMemoryItems() {
        const categories = ['code', 'concepts', 'patterns', 'paradigms', 'innovations'];
        const items = [];
        
        // Générer 20 éléments de mémoire
        for (let i = 0; i < 20; i++) {
            const category = categories[Math.floor(Math.random() * categories.length)];
            const createdDate = new Date();
            createdDate.setDate(createdDate.getDate() - Math.floor(Math.random() * 30));
            
            items.push({
                id: `mem-${i}`,
                category: category,
                title: this.getSampleTitle(category, i),
                summary: this.getSampleSummary(category),
                temperature: 36.5 + Math.random() * 2,
                created: createdDate.toISOString(),
                accesses: Math.floor(Math.random() * 50),
                relatedItems: Math.floor(Math.random() * 5)
            });
        }
        
        return items;
    }
    
    /**
     * Génère un titre d'exemple pour un élément de mémoire
     */
    getSampleTitle(category, index) {
        const titles = {
            code: [
                'Algorithme de tri optimisé',
                'Pattern Singleton en JavaScript',
                'Implémentation de Promise',
                'Framework React Components',
                'API REST avec Express'
            ],
            concepts: [
                'Polymorphisme en POO',
                'Modèle MVVM',
                'Architecture hexagonale',
                'Design thinking',
                'Event sourcing'
            ],
            patterns: [
                'Observer pattern',
                'Factory method',
                'Dependency injection',
                'Command pattern',
                'Strategy pattern'
            ],
            paradigms: [
                'Programmation fonctionnelle',
                'Programmation réactive',
                'Microservices',
                'Event-driven architecture',
                'Domain-driven design'
            ],
            innovations: [
                'Nouveau paradigme de programmation thermique',
                'IA auto-évolutive',
                'Système de mémoire multi-niveaux',
                'Algorithme d\'apprentissage continu',
                'Moteur d\'innovation cognitive'
            ]
        };
        
        const categoryTitles = titles[category] || titles.code;
        return categoryTitles[index % categoryTitles.length];
    }
    
    /**
     * Génère un résumé d'exemple pour un élément de mémoire
     */
    getSampleSummary(category) {
        const summaries = {
            code: 'Fragment de code permettant d\'optimiser les performances et d\'améliorer la lisibilité.',
            concepts: 'Concept fondamental permettant une meilleure organisation et structure du code.',
            patterns: 'Modèle de conception résolvant un problème courant de façon élégante et réutilisable.',
            paradigms: 'Approche conceptuelle pour organiser et structurer des systèmes complexes.',
            innovations: 'Nouvelle approche révolutionnaire pour améliorer l\'efficacité et la créativité de la programmation.'
        };
        
        return summaries[category] || '';
    }
    
    /**
     * Met à jour les statistiques affichées
     */
    updateStats() {
        // Mettre à jour les statistiques affichées
        const tempEl = document.getElementById('memory-temperature');
        const efficiencyEl = document.getElementById('memory-efficiency');
        const retrievalEl = document.getElementById('memory-retrieval');
        const consolidationEl = document.getElementById('memory-consolidation');
        const capacityEl = document.getElementById('memory-capacity');
        
        if (tempEl) tempEl.textContent = `${this.memoryStats.temperature.toFixed(1)}°C`;
        if (efficiencyEl) efficiencyEl.textContent = `${Math.round(this.memoryStats.efficiency * 100)}%`;
        if (retrievalEl) retrievalEl.textContent = `${Math.round(this.memoryStats.retrievalAccuracy * 100)}%`;
        if (consolidationEl) consolidationEl.textContent = `${Math.round(this.memoryStats.consolidationRate * 100)}%`;
        
        if (capacityEl) {
            const progressFill = capacityEl.querySelector('.progress-fill');
            const progressText = capacityEl.querySelector('.progress-text');
            
            if (progressFill) {
                progressFill.style.width = `${(this.memoryStats.used / this.memoryStats.capacity) * 100}%`;
            }
            
            if (progressText) {
                progressText.textContent = `${this.memoryStats.used}/${this.memoryStats.capacity}`;
            }
        }
    }
    
    /**
     * Met à jour la visualisation de la mémoire
     */
    updateVisualization() {
        if (!this.memoryVisualization) return;
        
        this.memoryVisualization.data.datasets[0].data = Object.values(this.memoryCategories).map(cat => cat.count);
        this.memoryVisualization.update();
    }
    
    /**
     * Affiche les éléments de mémoire
     */
    renderMemoryItems() {
        const container = document.getElementById('memory-items-list');
        if (!container) return;
        
        // Vider le conteneur
        container.innerHTML = '';
        
        // Ajouter les éléments
        this.memoryItems.forEach(item => {
            const itemEl = document.createElement('div');
            itemEl.className = `memory-item ${item.category}`;
            itemEl.dataset.category = item.category;
            
            // Formater la date
            const created = new Date(item.created);
            const formattedDate = created.toLocaleDateString('fr-FR', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric'
            });
            
            // Déterminer la classe de température
            let tempClass = 'normal';
            if (item.temperature > 38) tempClass = 'hot';
            if (item.temperature < 36.5) tempClass = 'cold';
            
            itemEl.innerHTML = `
                <div class="memory-item-header">
                    <span class="memory-item-category">${item.category}</span>
                    <span class="memory-item-temp ${tempClass}">${item.temperature.toFixed(1)}°C</span>
                </div>
                <div class="memory-item-title">${item.title}</div>
                <div class="memory-item-summary">${item.summary}</div>
                <div class="memory-item-footer">
                    <span class="memory-item-date">${formattedDate}</span>
                    <span class="memory-item-accesses">${item.accesses} accès</span>
                    <span class="memory-item-related">${item.relatedItems} connexions</span>
                </div>
            `;
            
            // Ajouter un événement de clic pour voir les détails
            itemEl.addEventListener('click', () => {
                this.showMemoryItemDetails(item);
            });
            
            container.appendChild(itemEl);
        });
    }
    
    /**
     * Filtre les éléments de mémoire par catégorie
     */
    filterMemoryItems(filter) {
        const items = document.querySelectorAll('.memory-item');
        
        items.forEach(item => {
            if (filter === 'all' || item.dataset.category === filter) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }
    
    /**
     * Affiche les détails d'un élément de mémoire
     */
    showMemoryItemDetails(item) {
        console.log('Détails de l\'élément de mémoire:', item);
        
        // TODO: Implémenter un modal pour afficher les détails
        alert(`Détails de l'élément "${item.title}" (à implémenter)`);
    }
    
    /**
     * Affiche l'analyseur de code
     */
    showCodeAnalyzer() {
        // Rediriger vers la page d'analyse de code
        window.location.href = '/code-analyzer.html';
    }
    
    /**
     * Affiche l'explorateur de paradigmes
     */
    showParadigmExplorer() {
        // Rediriger vers la page d'explorateur de paradigmes
        window.location.href = '/thermal-paradigm-explorer.html';
    }
    
    /**
     * Met à jour le statut basé sur les données du connecteur
     */
    updateStatus(status) {
        console.log('Mise à jour du statut de la mémoire:', status);
        
        // Mettre à jour les statistiques et la visualisation
        if (status && status.memory) {
            if (status.memory.itemCount !== undefined) {
                this.memoryStats.used = status.memory.itemCount;
            }
            
            if (status.memory.levels !== undefined) {
                this.memoryLevels = status.memory.levels;
            }
            
            this.updateStats();
            this.updateVisualization();
        }
    }
}

// Initialiser l'interface de mémoire thermique au chargement de la page
document.addEventListener('DOMContentLoaded', () => {
    // Vérifier si nous sommes sur la page de mémoire
    const memorySection = document.getElementById('memory-section');
    if (memorySection) {
        window.thermalMemoryInterface = new ThermalMemoryInterface();
        window.thermalMemoryInterface.initialize();
    }
});
