/**
 * Tableau de bord Kyber - Visualisations et contrôles pour l'accélérateur
 */

// État Kyber
let kyberState = {
    accelerationFactor: 1.8,
    temperature: 45.2,
    utilization: 0.42,
    performanceScore: 87,
    boostActive: false,
    boostEndTime: null,
    operationsCount: 2385,
    operationsPerSecond: 1240,
    cpuLoad: 0.32,
    memoryUsage: 128,
    efficiency: 0.84,
    lastUpdate: Date.now()
};

// Historique des données pour les graphiques
let kyberHistory = {
    timestamps: [],
    accelerationFactors: [],
    temperatures: [],
    utilizations: [],
    performanceScores: [],
    operationsPerMinute: []
};

// Références aux graphiques
let charts = {
    performance: null,
    temperature: null,
    utilization: null,
    operations: null,
    operationTypes: null,
    kyberPerformance: null
};

// Initialisation
document.addEventListener('DOMContentLoaded', () => {
    console.log('Initialisation du tableau de bord Kyber...');
    
    // Initialiser les éléments DOM
    initializeElements();
    
    // Initialiser les graphiques
    initializeCharts();
    
    // Configurer les événements
    setupEventListeners();
    
    // Démarrer les mises à jour
    startUpdates();
    
    // Animer la visualisation Kyber
    animateKyberVisualization();
});

// Initialisation des éléments DOM
function initializeElements() {
    // Mettre à jour les éléments avec les valeurs initiales
    updateDOMElements();
}

// Initialisation des graphiques
function initializeCharts() {
    // Remplir l'historique avec des données de test
    populateTestHistory();
    
    // Graphique des performances
    const performanceCtx = document.getElementById('performance-chart').getContext('2d');
    charts.performance = new Chart(performanceCtx, {
        type: 'line',
        data: {
            labels: kyberHistory.timestamps.slice(-20),
            datasets: [{
                label: 'Score',
                data: kyberHistory.performanceScores.slice(-20),
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                tension: 0.4,
                borderWidth: 2,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: false,
                    min: 60,
                    max: 100,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.05)'
                    }
                },
                x: {
                    display: false
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
    
    // Graphique de température
    const temperatureCtx = document.getElementById('temperature-chart').getContext('2d');
    charts.temperature = new Chart(temperatureCtx, {
        type: 'line',
        data: {
            labels: kyberHistory.timestamps.slice(-20),
            datasets: [{
                label: 'Température',
                data: kyberHistory.temperatures.slice(-20),
                borderColor: '#2ecc71',
                backgroundColor: 'rgba(46, 204, 113, 0.1)',
                tension: 0.4,
                borderWidth: 2,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: false,
                    min: 30,
                    max: 60,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.05)'
                    }
                },
                x: {
                    display: false
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
    
    // Graphique d'utilisation
    const utilizationCtx = document.getElementById('utilization-chart').getContext('2d');
    charts.utilization = new Chart(utilizationCtx, {
        type: 'line',
        data: {
            labels: kyberHistory.timestamps.slice(-20),
            datasets: [{
                label: 'Utilisation',
                data: kyberHistory.utilizations.slice(-20).map(u => u * 100),
                borderColor: '#f39c12',
                backgroundColor: 'rgba(243, 156, 18, 0.1)',
                tension: 0.4,
                borderWidth: 2,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: false,
                    min: 0,
                    max: 100,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.05)'
                    }
                },
                x: {
                    display: false
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
    
    // Graphique des opérations
    const operationsCtx = document.getElementById('operations-chart').getContext('2d');
    charts.operations = new Chart(operationsCtx, {
        type: 'bar',
        data: {
            labels: kyberHistory.timestamps,
            datasets: [{
                label: 'Opérations/min',
                data: kyberHistory.operationsPerMinute,
                backgroundColor: 'rgba(52, 152, 219, 0.5)',
                borderColor: 'rgba(52, 152, 219, 0.8)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.05)'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.05)'
                    }
                }
            }
        }
    });
    
    // Graphique des types d'opérations
    if (document.getElementById('operation-types-chart')) {
        const operationTypesCtx = document.getElementById('operation-types-chart').getContext('2d');
        charts.operationTypes = new Chart(operationTypesCtx, {
            type: 'doughnut',
            data: {
                labels: ['Lecture', 'Écriture', 'Recherche', 'Analyse', 'Optimisation'],
                datasets: [{
                    data: [35, 25, 15, 15, 10],
                    backgroundColor: [
                        'rgba(52, 152, 219, 0.8)',
                        'rgba(46, 204, 113, 0.8)',
                        'rgba(155, 89, 182, 0.8)',
                        'rgba(241, 196, 15, 0.8)',
                        'rgba(231, 76, 60, 0.8)'
                    ],
                    borderColor: 'rgba(0, 0, 0, 0.1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: 'rgba(255, 255, 255, 0.7)'
                        }
                    }
                }
            }
        });
    }
    
    // Graphique de performance Kyber
    if (document.getElementById('kyber-performance-chart')) {
        const kyberPerfCtx = document.getElementById('kyber-performance-chart').getContext('2d');
        charts.kyberPerformance = new Chart(kyberPerfCtx, {
            type: 'line',
            data: {
                labels: [...Array(24).keys()].map(i => `${i}:00`),
                datasets: [
                    {
                        label: 'Facteur d\'accélération',
                        data: generateRandomData(24, 1.5, 2.2),
                        borderColor: '#f39c12',
                        backgroundColor: 'rgba(0, 0, 0, 0)',
                        tension: 0.4,
                        borderWidth: 2,
                        yAxisID: 'y1'
                    },
                    {
                        label: 'Température',
                        data: generateRandomData(24, 40, 55),
                        borderColor: '#e74c3c',
                        backgroundColor: 'rgba(0, 0, 0, 0)',
                        tension: 0.4,
                        borderWidth: 2,
                        yAxisID: 'y2'
                    },
                    {
                        label: 'Score de performance',
                        data: generateRandomData(24, 70, 95),
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(0, 0, 0, 0)',
                        tension: 0.4,
                        borderWidth: 2,
                        yAxisID: 'y'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: false,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Score (0-100)',
                            color: 'rgba(255, 255, 255, 0.7)'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.05)'
                        }
                    },
                    y1: {
                        beginAtZero: false,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Facteur',
                            color: 'rgba(255, 255, 255, 0.7)'
                        },
                        grid: {
                            display: false
                        }
                    },
                    y2: {
                        beginAtZero: false,
                        position: 'right',
                        title: {
                            display: true,
                            text: '°C',
                            color: 'rgba(255, 255, 255, 0.7)'
                        },
                        grid: {
                            display: false
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.05)'
                        }
                    }
                }
            }
        });
    }
}

// Configuration des écouteurs d'événements
function setupEventListeners() {
    // Slider de boost
    const boostSlider = document.getElementById('boost-duration');
    const boostDurationValue = document.getElementById('boost-duration-value');
    
    if (boostSlider && boostDurationValue) {
        boostSlider.addEventListener('input', (e) => {
            const duration = e.target.value * 5; // 5, 10, 15, 20, 25 secondes
            boostDurationValue.textContent = duration;
        });
    }
    
    // Bouton d'activation du boost
    const boostButton = document.getElementById('activate-boost');
    if (boostButton) {
        boostButton.addEventListener('click', activateKyberBoost);
    }
    
    // Bouton de reconnexion
    const reconnectBtn = document.querySelector('.reconnect-btn');
    if (reconnectBtn) {
        reconnectBtn.addEventListener('click', reconnectKyberMemory);
    }
}

// Démarrer les mises à jour périodiques
function startUpdates() {
    // Mise à jour toutes les secondes
    setInterval(updateKyberState, 1000);
    
    // Mise à jour des graphiques toutes les 5 secondes
    setInterval(updateCharts, 5000);
}

// Générer des données d'historique de test
function populateTestHistory() {
    const now = Date.now();
    const timeInterval = 60 * 1000; // 1 minute
    
    for (let i = 0; i < 60; i++) {
        const timestamp = new Date(now - (59 - i) * timeInterval);
        kyberHistory.timestamps.push(timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }));
        
        // Simuler des fluctuations plausibles
        kyberHistory.accelerationFactors.push(1.5 + 0.5 * Math.sin(i / 10) + Math.random() * 0.2);
        kyberHistory.temperatures.push(45 + 5 * Math.sin(i / 15) + Math.random() * 2);
        kyberHistory.utilizations.push(0.3 + 0.2 * Math.sin(i / 12) + Math.random() * 0.1);
        kyberHistory.performanceScores.push(80 + 10 * Math.sin(i / 20) + Math.random() * 5);
        kyberHistory.operationsPerMinute.push(250 + 50 * Math.sin(i / 8) + Math.random() * 30);
    }
}

// Mettre à jour l'état de Kyber
function updateKyberState() {
    // Dans une application réelle, cela viendrait du serveur
    // Ici, nous simulons des changements légers
    
    // Vérifier si le boost est actif
    if (kyberState.boostActive && kyberState.boostEndTime) {
        const now = Date.now();
        if (now > kyberState.boostEndTime) {
            // Désactiver le boost
            kyberState.boostActive = false;
            kyberState.boostEndTime = null;
            kyberState.accelerationFactor = kyberState.accelerationFactor / 2; // Retour à la normale
        }
    }
    
    // Simuler des fluctuations naturelles
    kyberState.temperature += (Math.random() - 0.5) * 0.2;
    kyberState.temperature = Math.max(40, Math.min(55, kyberState.temperature));
    
    kyberState.utilization += (Math.random() - 0.5) * 0.02;
    kyberState.utilization = Math.max(0.2, Math.min(0.8, kyberState.utilization));
    
    kyberState.performanceScore += (Math.random() - 0.5) * 0.5;
    kyberState.performanceScore = Math.max(70, Math.min(95, kyberState.performanceScore));
    
    kyberState.operationsCount += Math.floor(Math.random() * 5);
    kyberState.operationsPerSecond = 1200 + Math.floor(Math.random() * 100);
    
    kyberState.lastUpdate = Date.now();
    
    // Mettre à jour les éléments DOM
    updateDOMElements();
}

// Mettre à jour les éléments DOM avec les valeurs actuelles
function updateDOMElements() {
    // Indicateurs principaux
    updateElement('acceleration-factor', `${kyberState.accelerationFactor.toFixed(1)}x`);
    updateElement('kyber-temp-value', `${kyberState.temperature.toFixed(1)}°C`);
    updateElement('utilization-value', `${Math.round(kyberState.utilization * 100)}%`);
    updateElement('performance-score', Math.round(kyberState.performanceScore));
    updateElement('operations-count', kyberState.operationsCount.toLocaleString());
    updateElement('kyber-temperature', `${kyberState.temperature.toFixed(1)}°C`);
    updateElement('memory-utilization', `${Math.round(kyberState.utilization * 100)}%`);
    updateElement('kyber-acceleration', `${kyberState.accelerationFactor.toFixed(1)}x`);
    
    // Indicateurs avancés
    updateElement('adv-acceleration-factor', `${kyberState.accelerationFactor.toFixed(1)}x`);
    updateElement('adv-temperature', `${kyberState.temperature.toFixed(1)}°C`);
    updateElement('adv-cpu-load', `${Math.round(kyberState.cpuLoad * 100)}%`);
    updateElement('adv-memory-usage', `${kyberState.memoryUsage}MB`);
    updateElement('adv-efficiency', `${Math.round(kyberState.efficiency * 100)}%`);
    updateElement('adv-ops-per-sec', kyberState.operationsPerSecond.toLocaleString());
    
    // Indicateur de boost
    const boostIndicator = document.getElementById('boost-indicator');
    if (boostIndicator) {
        boostIndicator.className = kyberState.boostActive ? 'kyber-boost-indicator active' : 'kyber-boost-indicator';
    }
    
    // Barres de progression
    updateProgressBar('utilization-chart', kyberState.utilization * 100);
    updateProgressBar('temperature-chart', (kyberState.temperature - 30) / 30 * 100); // Normaliser 30-60°C à 0-100%
    updateProgressBar('performance-chart', kyberState.performanceScore);
}

// Mettre à jour un élément DOM s'il existe
function updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = value;
    }
}

// Mettre à jour une barre de progression
function updateProgressBar(parentId, percentage) {
    const parent = document.getElementById(parentId);
    if (parent) {
        const progressBar = parent.closest('.monitor-card').querySelector('.progress-fill');
        if (progressBar) {
            progressBar.style.width = `${percentage}%`;
        }
    }
}

// Mettre à jour les graphiques
function updateCharts() {
    // Ajouter de nouvelles données
    const now = new Date();
    kyberHistory.timestamps.push(now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }));
    kyberHistory.accelerationFactors.push(kyberState.accelerationFactor);
    kyberHistory.temperatures.push(kyberState.temperature);
    kyberHistory.utilizations.push(kyberState.utilization);
    kyberHistory.performanceScores.push(kyberState.performanceScore);
    kyberHistory.operationsPerMinute.push(kyberState.operationsPerSecond * 60);
    
    // Limiter la taille des tableaux
    if (kyberHistory.timestamps.length > 60) {
        kyberHistory.timestamps.shift();
        kyberHistory.accelerationFactors.shift();
        kyberHistory.temperatures.shift();
        kyberHistory.utilizations.shift();
        kyberHistory.performanceScores.shift();
        kyberHistory.operationsPerMinute.shift();
    }
    
    // Mettre à jour les graphiques
    updateChart(charts.performance, kyberHistory.timestamps.slice(-20), kyberHistory.performanceScores.slice(-20));
    updateChart(charts.temperature, kyberHistory.timestamps.slice(-20), kyberHistory.temperatures.slice(-20));
    updateChart(charts.utilization, kyberHistory.timestamps.slice(-20), kyberHistory.utilizations.slice(-20).map(u => u * 100));
    
    if (charts.operations) {
        charts.operations.data.labels = kyberHistory.timestamps;
        charts.operations.data.datasets[0].data = kyberHistory.operationsPerMinute;
        charts.operations.update();
    }
}

// Mettre à jour un graphique spécifique
function updateChart(chart, labels, data) {
    if (chart) {
        chart.data.labels = labels;
        chart.data.datasets[0].data = data;
        chart.update();
    }
}

// Activer le boost Kyber
function activateKyberBoost() {
    if (kyberState.boostActive) {
        console.log('Le boost Kyber est déjà actif');
        return;
    }
    
    // Obtenir la durée du slider
    const boostSlider = document.getElementById('boost-duration');
    const duration = boostSlider ? parseInt(boostSlider.value) * 5 : 10; // Par défaut 10 secondes
    
    console.log(`Activation du boost Kyber pour ${duration} secondes`);
    
    // Mettre à jour l'état
    kyberState.boostActive = true;
    kyberState.boostEndTime = Date.now() + duration * 1000;
    kyberState.accelerationFactor *= 2; // Doubler le facteur d'accélération
    
    // Notifier l'utilisateur
    alert(`Boost Kyber activé pour ${duration} secondes. Facteur d'accélération: ${kyberState.accelerationFactor.toFixed(1)}x`);
    
    // Mettre à jour immédiatement les DOM
    updateDOMElements();
    
    // Animer la visualisation
    animateBoost();
}

// Reconnecter Kyber à la mémoire
function reconnectKyberMemory() {
    console.log('Tentative de reconnexion Kyber-Mémoire...');
    
    // Simuler une reconnexion
    const connectionIndicator = document.querySelector('.connection-indicator');
    if (connectionIndicator) {
        connectionIndicator.className = 'connection-indicator connecting';
        connectionIndicator.innerHTML = '<i class="fas fa-sync fa-spin"></i><span>Reconnexion en cours...</span>';
        
        setTimeout(() => {
            connectionIndicator.className = 'connection-indicator connected';
            connectionIndicator.innerHTML = '<i class="fas fa-check-circle"></i><span>Kyber connecté à la mémoire thermique</span>';
            
            // Mettre à jour les détails de connexion
            const details = document.querySelector('.connection-details');
            if (details) {
                const now = new Date();
                details.innerHTML = `
                    <div>Dernière connexion: ${now.toLocaleString()}</div>
                    <div>Temps depuis reconnexion: 0 minutes</div>
                    <div>État: Stable</div>
                `;
            }
        }, 2000);
    }
}

// Animer la visualisation Kyber
function animateKyberVisualization() {
    const kyberCore = document.querySelector('.kyber-core');
    const kyberRings = document.querySelectorAll('.kyber-ring');
    const kyberPulse = document.querySelector('.kyber-pulse');
    
    if (!kyberCore || !kyberRings.length || !kyberPulse) {
        return;
    }
    
    // Créer des particules
    createKyberParticles();
    
    // Animation des pulsations
    setInterval(() => {
        const newPulse = document.createElement('div');
        newPulse.className = 'kyber-pulse';
        document.querySelector('.kyber-visualization').appendChild(newPulse);
        
        // Supprimer après l'animation
        setTimeout(() => {
            newPulse.remove();
        }, 3000);
    }, 3000);
}

// Créer des particules Kyber
function createKyberParticles() {
    const container = document.querySelector('.kyber-visualization');
    if (!container) {
        return;
    }
    
    for (let i = 0; i < 20; i++) {
        const particle = document.createElement('div');
        particle.className = 'kyber-particle';
        container.appendChild(particle);
        
        // Position aléatoire
        const angle = Math.random() * Math.PI * 2;
        const distance = 20 + Math.random() * 80;
        const x = 50 + Math.cos(angle) * distance;
        const y = 50 + Math.sin(angle) * distance;
        
        particle.style.left = `${x}%`;
        particle.style.top = `${y}%`;
        
        // Animation
        animateKyberParticle(particle);
    }
}

// Animer une particule Kyber
function animateKyberParticle(particle) {
    const container = document.querySelector('.kyber-visualization');
    if (!container) {
        return;
    }
    
    const centerX = container.offsetWidth / 2;
    const centerY = container.offsetHeight / 2;
    
    const x = parseInt(particle.style.left);
    const y = parseInt(particle.style.top);
    
    const currentX = x / 100 * container.offsetWidth;
    const currentY = y / 100 * container.offsetHeight;
    
    // Calculer l'angle et la distance
    const dx = currentX - centerX;
    const dy = currentY - centerY;
    const angle = Math.atan2(dy, dx);
    const distance = Math.sqrt(dx * dx + dy * dy);
    
    // Nouvelle position
    const speed = 0.5 + Math.random() * 1;
    const newAngle = angle + (Math.random() - 0.5) * 0.5;
    const newDistance = distance + (Math.random() - 0.5) * 10;
    
    const newX = centerX + Math.cos(newAngle) * newDistance;
    const newY = centerY + Math.sin(newAngle) * newDistance;
    
    // Appliquer la transition
    particle.style.transition = `all ${speed}s ease-in-out`;
    particle.style.left = `${newX / container.offsetWidth * 100}%`;
    particle.style.top = `${newY / container.offsetHeight * 100}%`;
    
    // Continuer l'animation
    setTimeout(() => {
        animateKyberParticle(particle);
    }, speed * 1000);
}

// Animer l'effet de boost
function animateBoost() {
    const container = document.querySelector('.kyber-visualization');
    if (!container) {
        return;
    }
    
    // Ajouter une classe à la visualisation
    container.classList.add('boost-active');
    
    // Créer un effet d'explosion
    const explosion = document.createElement('div');
    explosion.className = 'kyber-explosion';
    container.appendChild(explosion);
    
    // Supprimer après l'animation
    setTimeout(() => {
        explosion.remove();
        container.classList.remove('boost-active');
    }, 10000);
}

// Générer des données aléatoires pour les graphiques
function generateRandomData(count, min, max) {
    return Array.from({ length: count }, () => min + Math.random() * (max - min));
}
