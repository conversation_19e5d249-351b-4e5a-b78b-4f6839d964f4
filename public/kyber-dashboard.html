<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de bord Kyber | Agent à Mémoire Thermique</title>
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="stylesheet" href="/css/neural-animation.css">
    <link rel="stylesheet" href="/css/kyber-styles.css">
    <link rel="stylesheet" href="/css/brain-activity-visualizations.css">
    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- Chart.js pour les graphiques -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
</head>
<body>
    <div class="app-container">
        <header>
            <div class="logo">
                <i class="fas fa-microchip"></i>
                <h1>Accélérateur Kyber | Tableau de bord</h1>
            </div>
            <div id="app-status" class="status disconnected">Déconnecté</div>
            <div class="kyber-status">
                <span id="kyber-indicator" class="status-indicator disabled"></span>
                <span id="kyber-label">Kyber</span>
                <button id="kyber-boost" class="kyber-boost-btn" title="Activer le boost Kyber"><i class="fas fa-bolt"></i></button>
            </div>
        </header>

        <nav>
            <ul>
                <li><a href="index.html"><i class="fas fa-arrow-left"></i> Retour</a></li>
                <li><a href="#acceleration" class="active"><i class="fas fa-tachometer-alt"></i> Accélération</a></li>
                <li><a href="#memory-sync"><i class="fas fa-link"></i> Synchro mémoire</a></li>
                <li><a href="#performance"><i class="fas fa-chart-line"></i> Performance</a></li>
                <li><a href="#optimization"><i class="fas fa-cogs"></i> Optimisation</a></li>
                <li><a href="#history"><i class="fas fa-history"></i> Historique</a></li>
            </ul>
        </nav>

        <main>
            <section id="acceleration" class="active">
                <h2>Tableau de bord de l'accélérateur Kyber</h2>
                <div class="dashboard-description">
                    <p>L'accélérateur Kyber optimise les opérations de la mémoire thermique et adapte l'allocation des ressources selon les besoins. Cette page permet d'analyser ses performances et son état actuel.</p>
                </div>

                <!-- Visualisation Kyber -->
                <div class="kyber-visualization">
                    <div class="kyber-core"></div>
                    <div class="kyber-ring" style="width: 80px; height: 80px;"></div>
                    <div class="kyber-ring" style="width: 140px; height: 140px;"></div>
                    <div class="kyber-ring" style="width: 200px; height: 200px;"></div>
                    <div class="kyber-pulse"></div>
                    <div class="kyber-temperature">45.2°C</div>
                    <div class="kyber-boost-indicator" id="boost-indicator">BOOST ACTIF</div>
                    <div class="kyber-stats">
                        <div>Facteur d'accélération: <span id="acceleration-factor">1.8x</span></div>
                        <div>Opérations: <span id="operations-count">2,385</span></div>
                    </div>
                </div>

                <!-- Système de monitoring avancé -->
                <div class="system-monitor">
                    <!-- Carte de performance -->
                    <div class="monitor-card">
                        <div class="monitor-card-header">
                            <div class="monitor-card-title">Performance</div>
                            <div class="monitor-card-subtitle">Score global</div>
                        </div>
                        <div class="monitor-card-value blue" id="performance-score">87</div>
                        <div class="progress-bar">
                            <div class="progress-fill blue" style="width: 87%"></div>
                        </div>
                        <div class="monitor-card-chart" id="performance-chart"></div>
                        <div class="monitor-card-footer">
                            <span>Dernière évaluation: 2 min</span>
                        </div>
                    </div>

                    <!-- Carte de température -->
                    <div class="monitor-card">
                        <div class="monitor-card-header">
                            <div class="monitor-card-title">Température</div>
                            <div class="monitor-card-subtitle">Celsius</div>
                        </div>
                        <div class="monitor-card-value green" id="kyber-temp-value">45.2°C</div>
                        <div class="progress-bar">
                            <div class="progress-fill green" style="width: 45%"></div>
                        </div>
                        <div class="monitor-card-chart" id="temperature-chart"></div>
                        <div class="monitor-card-footer">
                            <span>Plage optimale: 40-50°C</span>
                        </div>
                    </div>

                    <!-- Carte d'utilisation -->
                    <div class="monitor-card">
                        <div class="monitor-card-header">
                            <div class="monitor-card-title">Utilisation</div>
                            <div class="monitor-card-subtitle">Ressources</div>
                        </div>
                        <div class="monitor-card-value orange" id="utilization-value">42%</div>
                        <div class="progress-bar">
                            <div class="progress-fill orange" style="width: 42%"></div>
                        </div>
                        <div class="monitor-card-chart" id="utilization-chart"></div>
                        <div class="monitor-card-footer">
                            <span>Moyenne 15min: 38%</span>
                        </div>
                    </div>
                </div>

                <!-- Contrôle Kyber -->
                <div class="kyber-controls">
                    <div class="kyber-boost-control">
                        <h3>Contrôle du boost</h3>
                        <div class="boost-slider-container">
                            <input type="range" min="1" max="5" value="2" class="boost-slider" id="boost-duration">
                        </div>
                        <div class="boost-value">Durée: <span id="boost-duration-value">10</span> secondes</div>
                        <button class="boost-activate-btn" id="activate-boost">ACTIVER BOOST</button>
                    </div>

                    <div class="kyber-metrics">
                        <h3>Métriques avancées</h3>
                        <div class="metric-item">
                            <span class="metric-label">Facteur d'accélération:</span>
                            <span class="metric-value" id="adv-acceleration-factor">1.8x</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">Température:</span>
                            <span class="metric-value temperature-value" id="adv-temperature">45.2°C</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">Charge CPU:</span>
                            <span class="metric-value" id="adv-cpu-load">32%</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">Mémoire utilisée:</span>
                            <span class="metric-value" id="adv-memory-usage">128MB</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">Efficacité:</span>
                            <span class="metric-value" id="adv-efficiency">84%</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">Opérations/sec:</span>
                            <span class="metric-value" id="adv-ops-per-sec">1,240</span>
                        </div>
                    </div>
                </div>

                <!-- Historique des opérations -->
                <h3>Historique des opérations</h3>
                <div class="operations-chart-container">
                    <canvas id="operations-chart" height="250"></canvas>
                </div>
            </section>

            <section id="memory-sync">
                <h2>Synchronisation avec la mémoire thermique</h2>
                
                <!-- État de la connexion -->
                <div class="connection-status">
                    <div class="connection-indicator connected">
                        <i class="fas fa-check-circle"></i>
                        <span>Kyber connecté à la mémoire thermique</span>
                    </div>
                    <div class="connection-details">
                        <div>Dernière connexion: 06-05-2025 19:00:20</div>
                        <div>Temps depuis reconnexion: 22 minutes</div>
                        <div>État: Stable</div>
                    </div>
                    <button class="reconnect-btn">Reconnecter</button>
                </div>

                <!-- Visualisation de la synchronisation -->
                <h3>Flux de données mémoire-Kyber</h3>
                <div class="memory-kyber-flow">
                    <div class="memory-section">
                        <h4>Mémoire Thermique</h4>
                        <div class="memory-levels">
                            <div class="memory-level-item level1">Niveau 1</div>
                            <div class="memory-level-item level2">Niveau 2</div>
                            <div class="memory-level-item level3">Niveau 3</div>
                            <div class="memory-level-item level4">Niveau 4</div>
                            <div class="memory-level-item level5">Niveau 5</div>
                        </div>
                    </div>
                    <div class="flow-connection">
                        <div class="flow-arrow left-to-right"></div>
                        <div class="flow-arrow right-to-left"></div>
                        <div class="flow-stats">
                            <div>320 opérations/min</div>
                            <div>1.8 GB/heure</div>
                        </div>
                    </div>
                    <div class="kyber-section">
                        <h4>Accélérateur Kyber</h4>
                        <div class="kyber-components">
                            <div class="kyber-component optimizer">Optimiseur</div>
                            <div class="kyber-component regulator">Régulateur</div>
                            <div class="kyber-component cache">Cache</div>
                            <div class="kyber-component analyzer">Analyseur</div>
                        </div>
                    </div>
                </div>

                <!-- Statistiques de synchronisation -->
                <h3>Statistiques de synchronisation</h3>
                <div class="sync-stats">
                    <div class="sync-stat-item">
                        <div class="sync-stat-value">99.98%</div>
                        <div class="sync-stat-label">Intégrité</div>
                    </div>
                    <div class="sync-stat-item">
                        <div class="sync-stat-value">1.2ms</div>
                        <div class="sync-stat-label">Latence</div>
                    </div>
                    <div class="sync-stat-item">
                        <div class="sync-stat-value">320</div>
                        <div class="sync-stat-label">Opérations/min</div>
                    </div>
                    <div class="sync-stat-item">
                        <div class="sync-stat-value">0</div>
                        <div class="sync-stat-label">Erreurs</div>
                    </div>
                </div>
            </section>

            <section id="performance">
                <h2>Analyse de performance</h2>
                
                <!-- Graphique de performance sur le temps -->
                <div class="performance-chart-container">
                    <canvas id="kyber-performance-chart" height="300"></canvas>
                </div>
                
                <!-- Métriques de performance -->
                <div class="performance-metrics">
                    <div class="metric-card">
                        <div class="metric-icon"><i class="fas fa-bolt"></i></div>
                        <div class="metric-value" id="avg-acceleration">1.75x</div>
                        <div class="metric-label">Accélération moyenne</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-icon"><i class="fas fa-thermometer-half"></i></div>
                        <div class="metric-value" id="avg-temperature">44.8°C</div>
                        <div class="metric-label">Température moyenne</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-icon"><i class="fas fa-microchip"></i></div>
                        <div class="metric-value" id="total-operations">12,842</div>
                        <div class="metric-label">Total opérations</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-icon"><i class="fas fa-tachometer-alt"></i></div>
                        <div class="metric-value" id="max-acceleration">2.4x</div>
                        <div class="metric-label">Accél. maximale</div>
                    </div>
                </div>
                
                <!-- Analyse par type d'opération -->
                <h3>Performance par type d'opération</h3>
                <div class="operation-type-chart">
                    <canvas id="operation-types-chart" height="250"></canvas>
                </div>
            </section>
        </main>

        <footer>
            <p>Agent à Mémoire Thermique &copy; 2025 | <i class="fas fa-thermometer-half"></i> Système de mémoire thermique avec accélérateur Kyber</p>
            <div class="system-stats">
                <span id="kyber-temp">Kyber: <span id="kyber-temperature">45.2°C</span></span> | 
                <span id="memory-stat">Mémoire: <span id="memory-utilization">42%</span></span> |
                <span id="acceleration-factor">Accélération: <span id="kyber-acceleration">1.8x</span></span>
            </div>
        </footer>
    </div>
    
    <!-- Animation neuronale en arrière-plan pour tout le site -->
    <div id="neural-container" class="neural-background"></div>

    <script src="/js/neural-animation.js"></script>
    <script src="/js/enhanced-connector.js"></script>
    <script src="/js/kyber-integration.js"></script>
    <script src="/js/kyber-dashboard.js"></script>
</body>
</html>
